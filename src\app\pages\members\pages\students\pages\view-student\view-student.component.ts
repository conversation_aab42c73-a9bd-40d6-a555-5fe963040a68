import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges, ViewChild } from '@angular/core';
import { SharedModule } from 'src/app/shared/shared.module';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule, DatePipe } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { CommonUtils } from 'src/app/shared/utils';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { takeUntil } from 'rxjs';
import { CBGetResponse, CBResponse, IdNameModel, MatDialogRes } from 'src/app/shared/models';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { DashIfEmptyPipe, EnumToKeyValuePipe, LocalDatePipe } from 'src/app/shared/pipe';
import {
  AttendanceType,
  DependentInformationParams,
  DependentInformations,
  IdEmailModel,
  MakeUpPassParams,
  StudentAttendance,
  StudentGradeFormGroup,
  StudentGrades,
  StudentNotes
} from '../../models';
import { StudentPlanService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import {
  AddSchedule,
  ClassTypes,
  ScheduleDetailsView,
  StudentPlans
} from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { PlanSummaryService } from 'src/app/pages/settings/pages/plan/services';
import { Plan, PlanSummary } from 'src/app/pages/settings/pages/plan/models';
import { DependentService } from 'src/app/pages/profile/services';
import moment from 'moment';
import { CurrentUserScheduleLessonDetail } from 'src/app/pages/visits-scheduling/models';
import { StudentGradeService, StudentNotesService } from '../../services';
import { SignedDocumentService } from 'src/app/pages/user-document/services';
import { SignedDocuments } from 'src/app/pages/user-document/models';
import { MatSidenavModule } from '@angular/material/sidenav';
import { ViewStudentDocumentsComponent } from '../view-student-documents/view-student-documents.component';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Instrument } from 'src/app/request-information/models';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { GradeLevel, InstructorDetails, Instructors } from '../../../instructors/models';
import { AuthService } from 'src/app/auth/services';
import { InstructorService } from 'src/app/schedule-introductory-lesson/services';
import { SignUpForOptions } from 'src/app/auth/models';
import { Debounce } from 'src/app/shared/decorators';
import { AddScheduleComponent } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/pages/add-schedule/add-schedule.component';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { PassesService } from 'src/app/pages/settings/pages/passes/services';
import { AddStudentComponent } from '../add-student/add-student.component';
import { Account } from 'src/app/auth/models/user.model';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AssignedInstructors } from 'src/app/pages/schedule-classes/pages/ensemble-class/models';
import { ChatHistoryRes, ChatMessageType, FileType } from 'src/app/pages/messages/models';
import { ChatService } from 'src/app/pages/messages/services';
import { MessagesComponent } from '../../../../../messages/messages.component';
import { PaymentMethodsComponent } from 'src/app/shared/components/payment-methods/payment-methods.component';
import { AllAchOfUser, AllCardsOfUser, AllCustomerCards } from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { ContinueToCheckoutComponent } from '../continue-to-checkout/continue-to-checkout.component';
import { DateUtils } from 'src/app/shared/utils/date.utils';
import { StudentPastVisitComponent } from '../student-past-visit/student-past-visit.component';
import { SchedulerDetailPopupComponent } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/pages/scheduler-detail-popup/scheduler-detail-popup.component';
import { MbscModule, MbscPopup } from '@mobiscroll/angular';
import { POPUP_OPTIONS } from 'src/app/shared/constants';
import { UpdateScheduleComponent } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/pages/update-schedule/update-schedule.component';
import { ViewInstructorComponent } from '../../../instructors/pages/view-instructor/view-instructor.component';
import { Router } from '@angular/router';
import { PlansAndPasses } from 'src/app/pages/plans-and-passes/models';
import { PassInfo } from 'src/app/pages/settings/pages/passes/models';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { BillHistoryRes, UserBillingTransactionsResponse } from 'src/app/pages/billing/models';

const DEPENDENCIES = {
  MODULES: [
    CommonModule,
    MatButtonModule,
    SharedModule,
    MatIconModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatSidenavModule,
    ReactiveFormsModule,
    MatTooltipModule,
    FormsModule,
    MbscModule
  ],
  PIPES: [DashIfEmptyPipe, LocalDatePipe, EnumToKeyValuePipe],
  COMPONENTS: [
    ViewStudentDocumentsComponent,
    AddScheduleComponent,
    AddStudentComponent,
    MessagesComponent,
    PaymentMethodsComponent,
    ContinueToCheckoutComponent,
    StudentPastVisitComponent,
    SchedulerDetailPopupComponent,
    UpdateScheduleComponent,
    ViewInstructorComponent
  ]
};

@Component({
  selector: 'app-view-student',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './view-student.component.html',
  styleUrl: './view-student.component.scss'
})
export class ViewStudentComponent extends BaseComponent implements OnChanges {
  @Input() selectedStudentDetails!: DependentInformations | undefined;
  @Input() isFromAttendance = false;

  isViewDocumentSideNavOpen = false;
  isUpdateGrade = false;
  isPlanRenewalSideNavOpen = false;
  isScheduleALesson = false;
  isEnsembleAvailable = false;
  isMessageSideNavOpen = false;
  isPaymentMethodSideNavOpen = false;
  isAddNoteOpen = false;
  isPastVisitSideNavOpen = false;
  isEditLessonSideNavOpen = false;
  isInstructorDetailsSideNavOpen = false;
  showAllInstructor = false;
  showScheduleLoader = false;
  showGradeLoader = false;
  showSupervisorSchedule = false;
  showAccountManagerDetails = false;
  showEnsembleLoader = false;
  showMessageLoader = false;
  showPaymentMethodLoader = false;
  showStudentPlanLoader = false;
  showStudentNotesLoader = false;
  showAttendanceLoader = false;

  plans = Plan;
  classTypes = ClassTypes;
  chatTypes = ChatMessageType;
  fileTypes = FileType;
  attendanceTypes = AttendanceType;

  selectedStudentId!: number | undefined;
  selectedIdEmail!: IdEmailModel | null;
  ensemblePlans!: Array<StudentPlans>;
  otherPlans!: Array<StudentPlans>;
  dependentSchedule!: Array<CurrentUserScheduleLessonDetail>;
  studentGrades!: Array<StudentGrades>;
  studentDocuments!: Array<SignedDocuments>;
  allCustomerCards!: AllCustomerCards;
  defaultCard!: AllCardsOfUser | undefined;
  defaultAch!: AllAchOfUser | undefined;
  gradeFormGroup!: FormGroup<StudentGradeFormGroup>;
  instruments!: Array<Instrument>;
  gradeLevels!: Array<GradeLevel>;
  studentAttendance!: Array<StudentAttendance>;
  instructorIdsUnderSupervisor: Array<number> = [];
  expiringPlans!: Array<StudentPlans>;
  billHistoryDetails!: Array<UserBillingTransactionsResponse>;
  userTypes = SignUpForOptions;
  isAddDependentSideNavOpen!: boolean;
  isEditAccountManagerSideNavOpen!: boolean;
  accManagerDetails?: Account;
  chatHistory: Array<ChatHistoryRes> = [];
  studentNotes!: Array<StudentNotes>;
  studentPasses!: Array<PassInfo>;
  accountManagerUserTypes = SignUpForOptions;
  selectedStudentPlan!: PlanSummary | undefined;
  selectedInstrumentName!: string;
  selectedNoteId!: number | undefined;
  selectedInstructorDetails!: InstructorDetails | null;
  notes!: string;
  bookPlanFormValue!: AddSchedule;
  currentPage = this.paginationConfig.pageNumber;
  pageSize = this.paginationConfig.twoItemsPerPage;
  attendancePageSize = this.paginationConfig.itemsPerPage;
  selectedTabOption = AttendanceType.ALL;
  attendanceTotalCount!: number;

  filters: DependentInformationParams = {
    startDate: this.datePipe.transform(new Date(), this.constants.dateFormats.yyyy_MM_dd) ?? '',
    endDate: this.datePipe.transform(moment().add(6, 'days').toDate(), this.constants.dateFormats.yyyy_MM_dd) ?? '',
    dependentId: 0
  };

  @ViewChild('eventDetailsPopup', { static: false }) eventDetailsPopup!: MbscPopup;
  @ViewChild(SchedulerDetailPopupComponent) schedulerDetailPopupComponent!: SchedulerDetailPopupComponent;

  detailsAnchor!: EventTarget | null;
  selectedEvent!: ScheduleDetailsView | undefined;
  popupOptions = POPUP_OPTIONS;

  @Output() closeViewSideNav = new EventEmitter<void>();
  @Output() toggleAssignPlanAndProduct = new EventEmitter<StudentPlans[]>();
  @Output() openDependentDetails = new EventEmitter<number>();
  @Output() refreshStudentList = new EventEmitter<void>();

  constructor(
    private readonly cdr: ChangeDetectorRef,
    protected readonly planSummaryService: PlanSummaryService,
    private readonly studentPlanService: StudentPlanService,
    private readonly dependentService: DependentService,
    private readonly datePipe: DatePipe,
    private readonly studentGradeService: StudentGradeService,
    private readonly signedDocumentService: SignedDocumentService,
    private readonly commonService: CommonService,
    private readonly authService: AuthService,
    private readonly instructorService: InstructorService,
    private readonly dialog: MatDialog,
    private readonly passesService: PassesService,
    private readonly toasterService: AppToasterService,
    private readonly chatService: ChatService,
    private readonly studentNotesService: StudentNotesService,
    private readonly paymentService: PaymentService,
    private readonly router: Router
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.showPageLoader = true;
    if (changes['selectedStudentDetails']?.currentValue) {
      this.selectedStudentDetails = changes['selectedStudentDetails'].currentValue;
      if (
        !(
          this.selectedStudentDetails?.accountManagerUserType == SignUpForOptions.YOUR_CHILD &&
          this.selectedStudentDetails?.isAccountManager
        )
      ) {
        this.getCurrentUser();
        this.getStudentPlans(this.selectedStudentDetails!.id);
        this.getStudentGrades(this.selectedStudentDetails!.id);
        this.getDocumentById(this.selectedStudentDetails!.id);
        this.getStudentNotes(this.selectedStudentDetails!.id);
        this.getStudentPasses(this.selectedStudentDetails!.id);
        this.getStudentAttendance(this.currentPage, this.attendancePageSize, this.selectedTabOption);
        this.getInstruments();
        this.getGradeLevels();
        this.loadMessages();
        this.getExpiringPlans();
        this.getBillHistoryDetails();
        this.initGradeForm();
      }
      this.getAccountManagerDetails();
    }
  }

  initGradeForm(): void {
    this.gradeFormGroup = new FormGroup<StudentGradeFormGroup>({
      instrumentId: new FormControl(undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      studentId: new FormControl(this.selectedStudentDetails!.id, {
        nonNullable: true
      }),
      grade: new FormControl(undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      id: new FormControl(undefined, { nonNullable: true })
    });
  }

  getCurrentUser(): void {
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res;
          this.getInstructorIdsUnderSupervisor();
          this.updateScheduleOption(false);
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  getStudentPlans(studentId: number): void {
    this.showStudentPlanLoader = true;
    this.cdr.detectChanges();
    this.studentPlanService
      .getList<CBResponse<StudentPlans>>(`${API_URL.studentPlans.getStudentPlans}?DependentInformationId=${studentId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<StudentPlans>) => {
          const allPlans = res.result.items;
          this.ensemblePlans = allPlans.filter(plan => plan.isEnsembleAvailable);
          this.otherPlans = allPlans.filter(plan => !plan.isEnsembleAvailable);
          this.showStudentPlanLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showStudentPlanLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getStudentPasses(studentId: number): void {
    this.showPageLoader = true;
    this.cdr.detectChanges();
    this.studentPlanService
      .getList<CBGetResponse<PlansAndPasses>>(`${API_URL.studentPlans.getStudentPlansAndPasses}?dependentInformationId=${studentId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<PlansAndPasses>) => {
          this.studentPasses = res.result.passes.filter(
            pass => !pass.isUsed && !this.checkIfPassIsExpired(pass.expiryDate) && !pass.isDeleted
          );
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getInstructorNames(array: AssignedInstructors[]) {
    return array
      .slice(1)
      .map(item => item.instructorName)
      .join(', ');
  }

  getStudentGrades(studentId: number): void {
    this.showGradeLoader = true;
    this.studentGradeService
      .getList<CBResponse<StudentGrades>>(`${API_URL.studentGrades.getAllByStudentId}?studentId=${studentId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<StudentGrades>) => {
          this.studentGrades = res.result.items;
          this.showGradeLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showGradeLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getDocumentById(studentId: number): void {
    this.signedDocumentService
      .getList<CBResponse<SignedDocuments>>(`${API_URL.crud.getAll}?StudentId=${studentId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SignedDocuments>) => {
          this.studentDocuments = res.result.items;
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  getInstruments(): void {
    this.commonService
      .getInstruments()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          this.instruments = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getGradeLevels(): void {
    this.gradeLevels = [];
    for (let i = 0; i <= 10; i++) {
      this.gradeLevels.push({ gradeLevel: { id: i, name: String(i) } });
    }
  }

  getAccountManagerDetails(): void {
    this.authService
      .getUserDetailsFromId(this.selectedStudentDetails?.accountManagerId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.accManagerDetails = res.result;
          if (
            this.selectedStudentDetails?.accountManagerUserType === SignUpForOptions.YOUR_CHILD &&
            this.selectedStudentDetails?.isAccountManager
          ) {
            this.setShowAccountManagerDetails(true);
          }
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getExpiringPlans(): void {
    this.studentPlanService
      .getList<CBResponse<StudentPlans>>(
        `${API_URL.studentPlans.getAllStudentPlansExpiringSoon}?dependentInformationId=${this.selectedStudentDetails?.id}`
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<StudentPlans>) => {
          this.expiringPlans = res.result.items.filter(plan => plan.updatedAssignedPlanStatus === null);
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  getStudentNotes(studentId: number): void {
    this.showStudentNotesLoader = true;
    this.studentNotesService
      .getList<CBResponse<StudentNotes>>(`${API_URL.studentNotes.getAllNotes}?dependentInformationId=${studentId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<StudentNotes>) => {
          this.studentNotes = res.result.items;
          this.showStudentNotesLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showStudentNotesLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getFilterParamsForBillHistory() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      UserId: this.selectedStudentDetails?.accountManagerId,
      dependentInformationId: this.selectedStudentDetails?.id,
      CreatedStartDate: moment().subtract(1, 'month').format(this.constants.dateFormats.yyyy_MM_DD),
      CreatedEndDate: moment().format(this.constants.dateFormats.yyyy_MM_DD)
    });
  }

  getBillHistoryDetails(): void {
    this.showPageLoader = true;
    this.paymentService
      .getListWithFilters<CBResponse<UserBillingTransactionsResponse>>(this.getFilterParamsForBillHistory(), `${API_URL.payment.getAllTransactionsOfUser}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<UserBillingTransactionsResponse>) => {
          this.billHistoryDetails = res.result.items;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getTimeDiff(start: string, end: string): number {
    return CommonUtils.calculateTimeDifference(new Date(start), new Date(end));
  }

  checkIfPassIsExpired(expiryDate: string): boolean {
    return moment(expiryDate).isBefore(moment(), 'day');
  }

  getStudentAttendance(page: number, pageSize: number, status: number): void {
    this.showAttendanceLoader = true;
    this.selectedTabOption = status;
    this.dependentService
      .getList<CBResponse<StudentAttendance>>(
        `${API_URL.dependentInformations.getStudentAttendance}?page=${page}&pageSize=${pageSize}&studentId=${
          this.selectedStudentDetails!.id
        }&attendanceStatus=${status}`
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<StudentAttendance>) => {
          this.studentAttendance = res.result.items;
          this.attendanceTotalCount = res.result.totalCount;
          this.showAttendanceLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showAttendanceLoader = false;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getInstructorDetails(instructorId: number | undefined): void {
    this.instructorService
      .getList<CBGetResponse<Instructors>>(`${API_URL.instructorDetails.getInstructorDetailForEdit}?id=${instructorId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<Instructors>) => {
          this.selectedInstructorDetails = res.result.instructorDetail;
          this.cdr.detectChanges();
        }
      });
  }

  toggleSideNavForInstructor(isOpen: boolean, instructorId: number | undefined): void {
    this.isInstructorDetailsSideNavOpen = isOpen;
    if (instructorId && isOpen) {
      this.getInstructorDetails(instructorId);
    }
  }

  toggleMessageSideNav(isOpen: boolean): void {
    this.isMessageSideNavOpen = isOpen;
    this.selectedIdEmail = isOpen ? { id: this.selectedStudentDetails?.id, email: this.selectedStudentDetails?.accountManagerEmail } : null;
  }

  toggleAddNote(isOpen: boolean, note: StudentNotes | null): void {
    this.isAddNoteOpen = isOpen;
    this.notes = note?.notes || '';
    this.selectedNoteId = note?.id;
  }

  loadMessages(): void {
    this.showMessageLoader = true;
    this.chatService
      .add(
        {
          page: this.currentPage,
          pageSize: this.pageSize,
          studentId: this.selectedStudentDetails?.id,
          userEmail: this.selectedStudentDetails?.accountManagerEmail
        },
        API_URL.octopusChatAppServices.chatHistory
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<ChatHistoryRes>) => {
          this.chatHistory = res.result.items;
          this.showMessageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showMessageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  setActiveTabOption(tabValue: number): void {
    this.selectedTabOption = tabValue;
    this.getStudentAttendance((this.currentPage = 1), this.attendancePageSize, this.selectedTabOption);
  }

  getStudentAge(dob: string): number {
    return CommonUtils.getAgeFromDob(dob);
  }

  getFilterParams(instructorIds?: Array<number>) {
    const isInstructor = this.currentUser?.userRoleId === this.constants.roleIds.INSTRUCTOR;
    const instructorId = isInstructor || this.currentUser?.isSupervisor ? instructorIds : [];

    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      startDate: DateUtils.getUtcRangeForLocalDate(this.filters.startDate).startUtc,
      endDate: DateUtils.getUtcRangeForLocalDate(this.filters.endDate).endUtc,
      dependentId: this.selectedStudentDetails!.id,
      instructorIds: isInstructor || this.currentUser?.isSupervisor ? instructorId : [],
      isNotShowDraftSchedule:
        this.currentUser?.userRoleId === this.constants.roleIds.INSTRUCTOR ||
        this.currentUser?.userRoleId === this.constants.roleIds.SUPERVISOR
          ? true
          : false
    });
  }

  @Debounce(300)
  getDependentSchedule(instructorIds?: Array<number>): void {
    this.showScheduleLoader = true;
    this.cdr.detectChanges();
    this.dependentService
      .add(this.getFilterParams(instructorIds), API_URL.dependentInformations.getDependentSchedule)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<CurrentUserScheduleLessonDetail>) => {
          this.dependentSchedule = res.result.items;
          this.showScheduleLoader = false;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showScheduleLoader = false;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  updateScheduleOption(selectedOption: boolean): void {
    this.showSupervisorSchedule = selectedOption;

    const instructorIds = selectedOption ? [...this.instructorIdsUnderSupervisor] : [this.currentUser?.dependentId!];

    this.getDependentSchedule(instructorIds);
    this.cdr.detectChanges();
  }

  getInstructorIdsUnderSupervisor(): void {
    if (this.currentUser?.isSupervisor) {
      this.instructorService
        .getList<CBGetResponse<IdNameModel[]>>(
          `${API_URL.instructorDetails.getInstructorUnderSupervisor}?supervisorId=${this.currentUser?.dependentId}`
        )
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: CBGetResponse<IdNameModel[]>) => {
            this.instructorIdsUnderSupervisor = response.result.map(item => item.id);
            this.cdr.detectChanges();
          }
        });
    }
  }

  updateWeek(weeksToAdd: number): void {
    const startDate = moment(this.filters.startDate).add(weeksToAdd, 'week');
    const endDate = moment(startDate).add(6, 'days');

    this.filters.startDate = this.datePipe.transform(startDate.toDate(), this.constants.dateFormats.yyyy_MM_dd) ?? '';
    this.filters.endDate = this.datePipe.transform(endDate.toDate(), this.constants.dateFormats.yyyy_MM_dd) ?? '';
    this.updateScheduleOption(this.showSupervisorSchedule);
  }

  onUpdateGrade(): void {
    const instrumentId = this.gradeFormGroup.getRawValue().instrumentId;
    const matchingGrade = this.studentGrades.find(studentGrade => studentGrade.studentGrade.instrumentId === instrumentId);
    this.gradeFormGroup.controls.id.setValue(matchingGrade ? matchingGrade.studentGrade.id : undefined);

    this.showBtnLoader = true;
    this.studentGradeService
      .add(this.gradeFormGroup.getRawValue(), API_URL.crud.createOrEdit)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.resetGradeForm();
          this.getStudentGrades(this.selectedStudentDetails!.id);
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  onSaveNote(): void {
    if (!this.notes) {
      return;
    }
    this.showBtnLoader = true;
    this.studentNotesService
      .add(
        {
          id: this.selectedNoteId,
          dependentInformationId: this.selectedStudentDetails?.id,
          notes: this.notes
        },
        API_URL.crud.createOrEdit
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.getStudentNotes(this.selectedStudentDetails?.id!);
          this.toggleAddNote(false, null);
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  onDeleteNoteConfirmation(noteId: number): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Delete Note',
        message: 'Are you sure you want to delete this note?'
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.onDeleteNote(noteId);
      }
    });
  }

  onDeleteNote(noteId: number): void {
    this.studentNotesService
      .delete(noteId, API_URL.crud.delete)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.getStudentNotes(this.selectedStudentDetails?.id!);
          this.cdr.detectChanges();
        }
      });
  }

  resetGradeForm(): void {
    this.isUpdateGrade = false;
    this.gradeFormGroup.reset();
  }

  openConfirmationPopup(scheduleDetail: CurrentUserScheduleLessonDetail): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Assign Make Up Pass',
        message: `Are you sure you want to assign ${scheduleDetail.instrumentName} Lesson make up pass to ${scheduleDetail.studentName}?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.onAssignMakeUpPass(scheduleDetail);
      }
    });
  }

  openEventDetailsPopup(args: CurrentUserScheduleLessonDetail | undefined, event: MouseEvent): void {
    if (args?.id) {
      this.selectedEvent = args as unknown as ScheduleDetailsView;
    }
    this.detailsAnchor = event.currentTarget || event.target;
    this.eventDetailsPopup?.open();
  }

  onScheduleMakeUpLesson(studentPass: PassInfo): void {
    this.router.navigate([this.path.visits.root, this.path.scheduleMakeUpLesson], {
      queryParams: { scheduleId: studentPass.scheduleLessonId, d: studentPass.duration, isFromAdmin: true }
    });
  }

  onEditLesson(scheduleDetail: ScheduleDetailsView): void {
    this.isEditLessonSideNavOpen = true;
    this.selectedEvent = scheduleDetail;
    this.eventDetailsPopup.close();
  }

  closeEventDetailsPopup(shouldRefreshScheduleData: boolean): void {
    if (shouldRefreshScheduleData) {
      this.getDependentSchedule();
    }
    this.eventDetailsPopup.close();
  }

  getMakeUpPassParams(scheduleDetail: CurrentUserScheduleLessonDetail): MakeUpPassParams {
    return { scheduleId: scheduleDetail.id, studentId: scheduleDetail.studentId, instrumentId: scheduleDetail.instrumentId };
  }

  onAssignMakeUpPass(scheduleDetail: CurrentUserScheduleLessonDetail): void {
    this.passesService.add(this.getMakeUpPassParams(scheduleDetail), API_URL.passes.createMakeUpLessonPass).subscribe({
      next: () => {
        this.getDependentSchedule();
        this.toasterService.success(this.constants.successMessages.assignedSuccessfully.replace('{item}', 'Make Up Pass'));
        this.cdr.detectChanges();
      }
    });
  }

  openCancelPlanRequest(planId: number): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Cancel Plan',
        message: `Are you sure you want to cancel this plan?`,
        showReason: true
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.onCancelPlan(planId, result.reason);
      }
    });
  }

  onCancelPlan(planId: number, reason?: string): void {
    const reasonAdded = reason ? `&reason=${reason}` : '';
    this.studentPlanService
      .add({}, `${API_URL.studentPlans.cancelStudentAssignedPlan}?studentPlanId=${planId}${reasonAdded}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.constants.successMessages.canceledSuccessfully.replace('{item}', 'Plan'));
          this.getStudentPlans(this.selectedStudentDetails?.id!);
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  openPlanRenewalSideNav(studentPlan: StudentPlans): void {
    this.isPlanRenewalSideNavOpen = true;
    this.selectedStudentPlan = {
      ...(studentPlan as unknown as PlanSummary),
      plandetails: { items: studentPlan?.planDetails ?? [] },
      id: studentPlan?.studentplan.id ?? 0
    };
    this.selectedInstrumentName = studentPlan?.instrumentName ?? '';
    this.bookPlanFormValue = {
      ...(studentPlan as unknown as AddSchedule),
      scheduleStartTime: DateUtils.toLocal(studentPlan?.scheduleStartTime, 'yyyy-MM-DDTHH:mm:ss'),
      scheduleEndTime: DateUtils.toLocal(studentPlan?.scheduleEndTime, 'yyyy-MM-DDTHH:mm:ss'),
      scheduleDate: DateUtils.toLocal(studentPlan?.endDate, 'yyyy-MM-DDTHH:mm:ss'),
      daysOfSchedule: studentPlan?.daysOfSchedule ?? []
    };
  }

  shouldShowDivider(totalItemsLength: number, index: number, showAll: boolean, minShow: number): boolean {
    if (index === totalItemsLength - 1) {
      return false;
    }

    if (!showAll) {
      return index < minShow - 1;
    }

    return true;
  }

  getInitials(firstName?: string, lastName?: string): string {
    return CommonUtils.getInitials(firstName, lastName);
  }

  getInitialsFromFullName(name?: string): string {
    return CommonUtils.getInitialsUsingFullName(name);
  }

  openAssignPlanAndProduct(): void {
    this.toggleAssignPlanAndProduct.emit({ ...this.ensemblePlans, ...this.otherPlans });
  }

  onBookIntroductoryLesson() {
    this.isScheduleALesson = true;
    this.selectedStudentId = !this.showAccountManagerDetails
      ? this.selectedStudentDetails?.id
      : this.selectedStudentDetails?.accountManagerUserType === SignUpForOptions.YOUR_CHILD
      ? this.selectedStudentDetails?.accountManagerId
      : this.selectedStudentDetails?.accountManagerDependentId;
  }

  closeViewSideNavFun(): void {
    this.closeViewSideNav.emit();
    this.router.navigate([], { queryParams: {} });
  }

  onClickViewDependentDetails(studentId?: number): void {
    this.openDependentDetails.emit(studentId);
  }

  setShowAccountManagerDetails(showAccountManagerDetails: boolean): void {
    this.showAccountManagerDetails = showAccountManagerDetails;
  }

  onLessonSchedule(): void {
    this.isScheduleALesson = false;
    this.closeViewSideNavFun();
  }

  navigateToBillHistory(): void {
    this.router.navigate([this.path.billing.root, this.path.billing.planAndPass], {
      queryParams: { activeTab: 'Bill History', dependentId: this.selectedStudentDetails?.id, userId: this.selectedStudentDetails?.accountManagerId }
    });
  }

  keepOriginalOrder = () => 0;
}
