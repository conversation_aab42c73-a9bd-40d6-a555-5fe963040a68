@import "src/assets/scss/theme/_mixins.scss";
@import "src/assets/scss/variables";

.field-wrapper {
  @include flex-content-align-center;
  margin-bottom: 20px;
  min-height: 45px;

  label {
    color: $black-shade-text !important;
    font-size: 16px !important;
    font-weight: 600;
    min-width: 200px;
  }
}

::ng-deep {
  .mdc-label {
    font-size: 16px !important;
  }
}

@media (max-width: 530px) {
  .o-sidebar-header,
  .field-wrapper {
    flex-wrap: wrap;
  }

  .single-btn-select-wrapper .select-btn {
    padding: 12px 9px;
  }
}
